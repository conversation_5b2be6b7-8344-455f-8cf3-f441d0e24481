/* Estilos globales para arreglar problemas de modales */

/* Asegurar que todos los modales tengan z-index consistente */
.modal-overlay,
.modal-backdrop,
[class*="modal"] {
    z-index: 50 !important;
}

/* Prevenir scroll del body cuando hay modales abiertos */
body.modal-open {
    overflow: hidden;
}

/* Mejoras para modales en pantallas pequeñas */
@media (max-width: 640px) {
    .modal-content,
    [class*="modal"] > div {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
        max-height: calc(100vh - 2rem);
    }
}

/* Optimizaciones específicas para resolución 720p */
@media (max-width: 1366px) and (max-height: 768px) {
    .modal-content,
    [class*="modal"] > div {
        max-height: 85vh;
        font-size: 0.9rem;
    }
    
    .modal-content h1,
    .modal-content h2,
    .modal-content h3 {
        font-size: 1.1em;
        line-height: 1.3;
    }
    
    .modal-content p {
        font-size: 0.9em;
        line-height: 1.4;
    }
}

/* Mejoras para resolución HD Ready (1366x768) */
@media (min-width: 1280px) and (max-width: 1366px) and (max-height: 768px) {
    .modal-content {
        padding: 1rem 1.5rem;
    }
    
    .modal-content .gap-8 {
        gap: 1.5rem;
    }
    
    .modal-content .gap-6 {
        gap: 1rem;
    }
}

/* Asegurar que los modales siempre estén centrados */
.modal-overlay {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Prevenir que los modales se salgan de la pantalla */
.modal-content {
    position: relative;
    max-width: 100vw;
    max-height: 100vh;
    overflow: auto;
}

/* Mejoras para botones de cierre */
.modal-close-button {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    background: rgba(239, 68, 68, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.modal-close-button:hover {
    background: rgba(239, 68, 68, 1);
}

/* Mejoras para transiciones suaves */
.modal-transition-enter-active,
.modal-transition-leave-active {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-transition-enter-from,
.modal-transition-leave-to {
    opacity: 0;
}

.modal-transition-enter-from .modal-content,
.modal-transition-leave-to .modal-content {
    transform: scale(0.95) translateY(-1rem);
}

.modal-transition-enter-to,
.modal-transition-leave-from {
    opacity: 1;
}

.modal-transition-enter-to .modal-content,
.modal-transition-leave-from .modal-content {
    transform: scale(1) translateY(0);
}

/* Mejoras específicas para EvidenceModal */
.evidence-modal-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

@media (min-width: 1024px) {
    .evidence-modal-content {
        flex-direction: row;
        gap: 2rem;
    }
}

/* Mejoras para ModalLevel */
.modal-level-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

@media (min-width: 640px) {
    .modal-level-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .modal-level-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
}

/* Asegurar que los inputs y botones en modales sean accesibles */
.modal-content input,
.modal-content button,
.modal-content select {
    min-height: 2.5rem;
    font-size: 1rem;
}

@media (max-width: 640px) {
    .modal-content input,
    .modal-content button,
    .modal-content select {
        min-height: 3rem;
        font-size: 1.1rem;
    }
}
