@import './assets/styles/views/common.css';
@import './assets/styles/responsive-utilities.css';
@import './assets/styles/modal-fixes.css';
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&family=Inter:wght@300;500;700&display=swap');
@import 'primeicons/primeicons.css';
body {
  font-family: 'Poppins', sans-serif;
  background-color: #F2F2F2;
}

main {
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
  padding-left: 16px;
  padding-right: 16px;
  padding-top: 10px;
  padding-bottom: 20px;
}

/* Responsive padding para main */
@media (min-width: 768px) {
  main {
    padding-left: 24px;
    padding-right: 24px;
  }
}

@media (min-width: 1024px) {
  main {
    padding-left: 32px;
    padding-right: 32px;
  }
}

router-view {
  flex: 1 1 auto;
  overflow-y: auto;
}

/* Estilo para panel Superior*/
.logo {
  width: 50px;
  margin-right: 10px;
}

.logo-text {
  color: black;
  font-weight: 600;
  font-size: 1.7rem;
}

.blue-text {
  color: blue;
  font-weight: 800;
  font-size: 1.7rem;
}

/* Estilos para icons*/
.fa-check {
  color: #003574;
}

.fa-sync {
  color: #26D0FF;
}

.fa-tasks {
  color: #3D9B79;
}

.fa-play {
  color: #0057E6;
}

.fa-plus {
  color: #003574;
}

.fa-folder-plus {
  color: #003574;
}

/* Estilos para tabla*/
.table {
  background-color: white;
  width: 100%;
}