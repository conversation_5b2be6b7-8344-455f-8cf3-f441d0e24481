<script setup lang="ts">
import { EvidenceStatus } from '@/models/types';
import Button from '../atoms/Button.vue';
import EvidenceModal from '../molecules/EvidenceModal.vue';
import EvidenceTile from '../atoms/EvidenceTile.vue';
import EvidenceApprovalOptions from '../molecules/EvidenceApprovalOptions.vue';
import type { Dimension } from '@/models/types';
import { computed, onMounted, ref } from 'vue';
import { useEvidences } from '@/hooks/useEvidences';
import { useDimensions } from '@/hooks/useDimensions';
import EvidenceAppealTile from '../molecules/EvidenceAppealTile.vue';
import { useProcesses } from '@/hooks/process/useProcesses';
import { useAuth } from '@/hooks/useAuth';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';
import { useToast } from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';
import { useRouter, useRoute } from 'vue-router';
import DimensionsWhitQuestionAudit from './DimensionsWhitQuestionAudit.vue';
const $toast = useToast();
const props = defineProps({
    appealMode: {
        type: Boolean,
        required: false,
        default: false
    },
    id:{
        type: Number,
        required: true
    }
})

const processID = defineModel({
    type: Number,
    required: true
});
const actualDimension = ref<Dimension>()
const dimensions = ref<Dimension[]>([])
const nextStep = ref<boolean>(false);
const router = useRouter();
const route = useRoute();
const showEmptyModal = ref(false);
const { handleFinishProcess, handleAuditProcess } = useProcesses();
const { handleGetDimensionsByProcessID, handleGetAppealedDimensionsWithEvidences } = useDimensions();
const { handleApproveOrRejectEvidence } = useEvidences();

const onConfirm = async (evidenceId: number, status: EvidenceStatus, comment: string, onModalClose: () => void) => {
    if (actualDimension.value) {
        await handleApproveOrRejectEvidence(evidenceId, status, comment);
        onModalClose();
        nextStep.value = false;
        setTimeout(() => {
            dimensions.value.find(d => d.name === actualDimension.value!.name)!.evidences!.map(
                e => {
                    if (e.id === evidenceId) {
                        e.evidenceStatus = status;
                    }
                }
            )
        }, 300);
    }
}
const handleGetDimensions = async () => {
    dimensions.value = props.appealMode ?
        await handleGetAppealedDimensionsWithEvidences(props.id)
        : await handleGetDimensionsByProcessID(props.id);
        if (dimensions.value.length === 0) {
        showEmptyModal.value = true;
    } else {
        actualDimension.value = dimensions.value[0];
    }
};


const canFinish = computed(() => {
    const result = dimensions.value.every(dimension =>
        dimension.evidences?.every(evidence =>
            evidence.evidenceStatus !== 'NOT_EVALUATED' &&
            evidence.evidenceStatus !== 'APPEAL' &&
            evidence.uploaded
        )
    );
    return result;
});

const hasRejectedEvidence = computed(() => {
    return dimensions.value.some(dimension =>
        dimension.evidences?.some(evidence => evidence.evidenceStatus === 'REJECTED')
    );
});

const handleFinish = async () => {
    if (hasRejectedEvidence.value) {
        $toast.info('Debido a que se objetó una evidencia, queda a la espera de apelación y se mostrará en Apelaciones.');
        // Navegar a la página de apelaciones y forzar actualización
        await router.push('/auditor/APPEAL');
        return;
    }
    if (canFinish.value && props.appealMode) {
        const { authData } = useAuth();
        await handleAuditProcess(props.id);
        await handleFinishProcess(props.id, authData.value?.id!);
        // Navegar a la página de apelaciones y forzar actualización
        await router.push('/auditor/APPEAL');
        $toast.success('Apelación finalizada corrrectamente');
    }
    else if(canFinish.value && !props.appealMode){
        const { authData } = useAuth();
        await handleAuditProcess(props.id);
        await handleFinishProcess(props.id, authData.value?.id!);
        // Navegar a la página de procesos finalizados y forzar actualización
        await router.push('/auditor/FINISHED');
        $toast.success('Proceso finalizado correctamente');
    }
    else{
        $toast.warning('No se puede finalizar el proceso, hay evidencias pendientes.');
    }
}

const { fetchData: finishProcess, isLoading: isFinishingLoading } = useAsyncFetch(handleFinish)

onMounted(() => {
    handleGetDimensions();
});

</script>

<template>
    <DimensionsWhitQuestionAudit v-model="actualDimension!" :dimensions="dimensions"
        :showEvidenceStatusesLabel="true">
        <template #evidences="{ evidence }">
            <EvidenceAppealTile v-if="appealMode" :evidence="evidence" :auditorMode="true">
                <template #button-text>
                    Evaluar
                </template>
                <template #evidence-modal="{ evidence, isEvidenceModalOpen, onModalClose }">
                    <EvidenceModal :isEvidenceModalOpen="isEvidenceModalOpen" :onModalClose="onModalClose"
                        :evidence="evidence" :onEvidenceDelete="() => { }" :isAppealMode="appealMode" :isAuditMode="true" :onEvidenceUpload="() => { }">
                        <template v-if="nextStep" #modal-content>
                            <div class="hidden"></div>
                        </template>
                        <template #evidence-actions>
                            <EvidenceApprovalOptions v-model="nextStep" :evidenceId="evidence.id!"
                                :onConfirm="onConfirm" :onModalClose="onModalClose">
                            </EvidenceApprovalOptions>
                        </template>
                    </EvidenceModal>
                </template>

            </EvidenceAppealTile>
            <EvidenceTile v-else :auditorMode="true" :key="evidence.title" :onEvidenceUpload="() => { }"
                :evidence="evidence">
                <template #evidence-modal="{ evidence, isEvidenceModalOpen, onModalClose }">
                    <EvidenceModal v-if="(evidence.evidenceStatus === EvidenceStatus.NOT_EVALUATED)"
                        :isEvidenceModalOpen="isEvidenceModalOpen" :onModalClose="onModalClose" :evidence="evidence"
                        :onEvidenceDelete="() => { }" :isAppealMode="appealMode" :onEvidenceUpload="() => { }">
                        <template v-if="nextStep" #modal-content>
                            <div class="hidden"></div>
                        </template>
                        <template #evidence-actions>
                            <EvidenceApprovalOptions v-model="nextStep" :evidenceId="evidence.id!"
                                :onConfirm="onConfirm" :onModalClose="onModalClose">
                            </EvidenceApprovalOptions>
                        </template>
                    </EvidenceModal>
                </template>
            </EvidenceTile>
        </template>
          <template v-if="true" #end-button>
            <Button @click="finishProcess" :loading="isFinishingLoading" :disabled="!canFinish">Terminar revisión</Button>
        </template>
    </DimensionsWhitQuestionAudit>

    <div v-if="showEmptyModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 p-4">
        <div class="bg-white p-6 rounded-lg shadow-2xl text-center max-w-[95vw] sm:max-w-md w-full">
            <h2 class="text-lg sm:text-xl font-semibold mb-3">No se encontraron datos</h2>
            <p class="text-sm sm:text-base text-gray-600 mb-6">No hay dimensiones o evidencias disponibles en este proceso.</p>
            <button @click="showEmptyModal = false" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                Cerrar
            </button>
        </div>
    </div>
</template>