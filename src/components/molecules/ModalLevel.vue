<template>
    <div v-if="isVisible" class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
      <div class="bg-background rounded-lg shadow-lg p-4 sm:p-6 w-full max-w-[95vw] sm:max-w-[90vw] lg:max-w-6xl xl:max-w-7xl max-h-[90vh] overflow-y-auto">
        <header class="flex justify-between items-center mb-4">
          <h3 class="text-xl sm:text-2xl font-semibold">Nivel de dimensión actual</h3>
          <button @click="closeModal" class="text-2xl sm:text-3xl hover:text-gray-600 transition-colors">&times;</button>
        </header>
        <p class="text-gray-500 text-base sm:text-lg mb-6 sm:mb-8">En mi institución</p>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
          <div v-for="(dimension, index) in dimensions" :key="index" class="bg-white p-4 sm:p-6 lg:p-8 rounded-lg shadow-sm">
            <div class="flex justify-between items-start mb-4 sm:mb-6">
              <div class="flex-1 pr-4">
                <h4 class="font-semibold text-lg sm:text-xl lg:text-2xl mb-2">{{ dimension.title }}</h4>
                <p class="text-sm sm:text-base text-gray-500 leading-relaxed">{{ dimension.description }}</p>
              </div>
              <div :class="`p-3 sm:p-4 rounded-lg ${iconBackground(index)} icon-container flex-shrink-0`">
                <i :class="`fa-solid ${dimension.icon} text-xl sm:text-2xl lg:text-3xl text-white`"></i>
              </div>
            </div>
            <div class="mt-4 sm:mt-6 lg:mt-8">
              <p class="text-2xl sm:text-3xl font-bold text-indigo-700">Nivel {{ dimension.level }}</p>
              <p class="text-sm sm:text-base text-gray-500 mt-2 sm:mt-4 leading-relaxed">{{ dimension.levelDescription }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { defineProps, defineEmits } from 'vue';
  
  interface Dimension {
    title: string;
    description: string;
    level: number;
    levelDescription: string;
    icon: string;
  }
  
  const props = defineProps<{ isVisible: boolean }>();
  const emit = defineEmits(['close']);
  
  const iconBackground = (index: number) => {
    const colors = ['bg-purple-600', 'bg-green-600', 'bg-pink-500', 'bg-red-500', 'bg-blue-500', 'bg-yellow-500'];
    return colors[index % colors.length]; 
  };
  
  const dimensions: Dimension[] = [
    {
      title: 'Institucionalización',
      description: 'Proceso de alinear la visión y estrategias de transparencia con las directrices nacionales.',
      level: 5,
      levelDescription: 'La institución es un referente en términos de formalización y estandarización de procesos.',
      icon: 'fa-clipboard-list'
    },
    {
      title: 'Adquisición de bienes',
      description: 'Proceso de adquisición de bienes formalizado con métricas basadas en resultados históricos.',
      level: 3,
      levelDescription: 'Se siguen pautas y comparaciones de precios y calidad con regularidad.',
      icon: 'fa-shopping-bag'
    },
    {
      title: 'Adquisición de servicios',
      description: 'Proceso estandarizado para adquisición de servicios con control de calidad.',
      level: 3,
      levelDescription: 'El proceso incluye la comparación de proveedores y la verificación de calidad.',
      icon: 'fa-headset'
    },
    {
      title: 'Contratación de personal',
      description: 'Proceso controlado para contratación con algunos criterios de selección.',
      level: 2,
      levelDescription: 'Se aplican criterios, pero aún hay margen para mejorar la consistencia.',
      icon: 'fa-user-plus'
    },
    {
      title: 'Comunicación',
      description: 'Los canales de comunicación son informales y necesitan mejora.',
      level: 1,
      levelDescription: 'La falta de comunicación afecta la coordinación y los resultados.',
      icon: 'fa-bullhorn'
    },
    {
      title: 'Rendición de cuentas',
      description: 'Rendición de cuentas completamente transparente y con auditorías regulares.',
      level: 5,
      levelDescription: 'Los procesos están sujetos a auditorías regulares y abiertas al público.',
      icon: 'fa-file-invoice'
    }
  ];
  
  const closeModal = () => {
    emit('close');
  };
  </script>
  
  <style scoped>
  .icon-container {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  @media (min-width: 640px) {
    .icon-container {
      width: 56px;
      height: 56px;
    }
  }

  @media (min-width: 1024px) {
    .icon-container {
      width: 60px;
      height: 60px;
    }
  }
  </style>
  