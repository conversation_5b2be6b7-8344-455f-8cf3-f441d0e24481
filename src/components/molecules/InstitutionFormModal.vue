<script setup lang="ts">
import Modal from '../atoms/Modal.vue';
import TabBar from './TabBar.vue';
import type { Institution } from '@/models/types';
import { useInstitutionsFormSteps } from '@/hooks/useInstitutionsFormSteps';
import { ref, watch } from 'vue';

const props = defineProps({
    institution: {
        type: Object as () => Institution,
        required: false
    },
    isModalOpen: {
        type: Boolean,
        required: true
    },
    onClose: {
        type: Function,
        required: true
    },
    enableAuditor: {
        type: Boolean,
        required: true
    },
    enable: {
        type: Boolean,
        required: true
    }
});

const institutionData = ref<Institution | undefined>();
const handleClose = () => {
    props.onClose();
};
watch(() => props.institution, (newVal) => {
    institutionData.value = newVal;
}, { immediate: true });

const {
    isUpdate,
    activeTabComponent: ActiveTabComponent,
    activeTab,
    setActiveTab,
    nextStep,
    prevStep,
    getCurrentStepInfo,
    tabs
} = useInstitutionsFormSteps(institutionData);


</script>

<template>
    <Modal :isOpen="isModalOpen" :onClose="onClose">
        <div class="flex flex-col gap-6 sm:gap-8 w-full max-w-[95vw] sm:max-w-[85vw] md:max-w-[75vw] lg:max-w-[65vw] xl:max-w-[60vw] 2xl:max-w-[55vw] h-auto max-h-[85vh] 2xl:max-h-[80vh] relative">
            <button class="absolute top-4 right-4 w-8 h-8 rounded-full bg-red-600 text-white text-2xl flex items-center justify-center font-bold" @click="handleClose">
                &times;
            </button>

            <div class="flex flex-col gap-3">
                <h3 class="flex flex-col gap-2.5 text-xl font-bold">{{ activeTab.title }}
                    <p class="text-sm font-normal">
                        {{ activeTab.description }}
                    </p>
                </h3>
                <TabBar :effect="`${isUpdate ? 'tab' : 'progress'}`" :onTabChange="institution && setActiveTab"
                    :tabs="tabs" :activeTab="activeTab.name" :auditorEnable="props.enable" />
            </div>
            <div class="flex flex-col justify-between flex-1">
                <div class="flex flex-1 w-full gap-10">
                    <component :institution="institution" :update="isUpdate"
                        :savedInstitutionData="getCurrentStepInfo()" :is="ActiveTabComponent" :onNext="nextStep"
                        @prev="prevStep" :enable="props.enable" />
                </div>
            </div>
        </div>
    </Modal>
</template>
