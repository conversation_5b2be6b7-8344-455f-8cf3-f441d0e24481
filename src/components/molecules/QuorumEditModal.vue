<script setup lang="ts">
import Modal from '../atoms/Modal.vue'
import Button from '../atoms/Button.vue'
import Input from '../atoms/Input.vue'
import { ref, watch, computed } from 'vue'
import type { Process } from '@/models/types'
import { useToast } from 'vue-toast-notification'

const toast = useToast()

const props = defineProps({
  isModalOpen: {
    type: Boolean,
    required: true
  },
  onClose: {
    type: Function,
    required: true
  },
  process: {
    type: Object as () => Process | null,
    default: null
  },
  onSave: {
    type: Function as unknown as () => (processId: number, quorum: number) => Promise<void>,
    required: true
  }
})

const quorumValue = ref<string>('')
const isLoading = ref(false)
const errorMessage = ref<string>('')

// Convertir el valor del quorum a porcentaje para mostrar al usuario
const quorumPercentage = computed({
  get: () => {
    const numValue = parseFloat(quorumValue.value)
    return isNaN(numValue) ? '' : (numValue * 100).toString()
  },
  set: (value: string) => {
    const numValue = parseFloat(value)
    if (isNaN(numValue)) {
      quorumValue.value = ''
    } else {
      quorumValue.value = (numValue / 100).toString()
    }
  }
})

// Validar el valor del quorum
const validateQuorum = (value: string): string => {
  console.log('Validando quorum', value)
  if (value === '0') return ''

  if (!value.trim()) {
    return 'El quorum es requerido'
  }
  
  const numValue = parseFloat(value)
  if (isNaN(numValue)) {
    return 'El quorum debe ser un número válido'
  }
  
  if (numValue < 0 || numValue > 1) {
    return 'El quorum debe estar entre 0 y 1 (0% y 100%)'
  }
  
  return ''
}

// Validar el valor del porcentaje mostrado al usuario
const validatePercentage = (value: string): string => {
  if (value === '0') return ''
  if (!value.trim()) {
    return 'El quorum es requerido'
  }
  
  const numValue = parseFloat(value)
  if (isNaN(numValue)) {
    return 'El quorum debe ser un número válido'
  }
  
  if (numValue < 0 || numValue > 100) {
    return 'El quorum debe estar entre 0% y 100%'
  }
  
  return ''
}

const isFormValid = computed(() => {
  return quorumValue.value !== '' && validateQuorum(quorumValue.value) === ''
})

// Inicializar el valor cuando se abre el modal o cambia el proceso
watch(() => [props.isModalOpen, props.process], ([isOpen, newProcess]) => {
  if (isOpen && newProcess && typeof newProcess === 'object') {
    // Si el proceso tiene approvalThreshold, usarlo; si no, usar 0.5 (50%) por defecto
    const threshold = newProcess.approvalThreshold ?? 0.5
    quorumValue.value = threshold.toString()
    errorMessage.value = ''
  } else if (!isOpen) {
    // Limpiar cuando se cierra el modal
    errorMessage.value = ''
    isLoading.value = false
  }
}, { immediate: true })

const handleSave = async () => {
  if (!props.process || !isFormValid.value) {
    return
  }

  const error = validateQuorum(quorumValue.value)
  if (error) {
    errorMessage.value = error
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const quorumNumber = parseFloat(quorumValue.value)
    await props.onSave(props.process.id, quorumNumber)
    props.onClose()
  } catch (error) {
    console.error('Error updating quorum:', error)
    errorMessage.value = 'Error al actualizar el quorum. Intente nuevamente.'
  } finally {
    isLoading.value = false
  }
}

const handleCancel = () => {
  props.onClose()
}

// Formatear el input para mostrar solo números y punto decimal
const formatQuorumInput = (value: string) => {
  // Permitir solo números, punto decimal y máximo 2 decimales
  return value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')
}
</script>

<template>
  <Modal :isOpen="isModalOpen" :onClose="onClose">
    <div class="flex flex-col gap-6">
      <div class="flex flex-col gap-2">
        <h2 class="text-xl font-bold">Editar Quorum de Aprobación</h2>
        <p class="text-sm text-gray-600">
          Modifica el quorum de aprobación para el proceso 
          <span class="font-semibold">{{ process?.name }}</span>
          de la institución 
          <span class="font-semibold">{{ process?.simpleInstitutionDTO?.name }}</span>
        </p>
      </div>

      <div class="flex flex-col gap-4">
        <div class="flex flex-col gap-2">
          <label class="font-semibold text-sm">
            Quorum de Aprobación (%)
          </label>
          <Input
            v-model="quorumPercentage"
            type="number"
            placeholder="50"
            :errorMessage="errorMessage || validatePercentage(quorumPercentage)"
            :formatter="formatQuorumInput"
            :disabled="isLoading"
            min="0"
            max="100"
            step="0.1"
          />
          <p class="text-xs text-gray-500">
            Ingrese un valor entre 0% y 100%. El valor por defecto es 50%.
          </p>
        </div>

        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="fas fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-3">
              <p class="text-sm text-blue-700">
                <strong>Información:</strong> El quorum de aprobación determina el porcentaje mínimo 
                de respuestas positivas requeridas para aprobar cada dimensión del proceso.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-end gap-4 pt-4 border-t">
        <Button
          variant="secondary"
          @click="handleCancel"
          :disabled="isLoading"
          class="px-6"
        >
          Cancelar
        </Button>
        <Button
          @click="handleSave"
          :loading="isLoading"
          :disabled="!isFormValid"
          class="px-6"
        >
          Guardar Cambios
        </Button>
      </div>
    </div>
  </Modal>
</template>
