<script setup lang="ts">
import type { Evidence } from '@/models/types';
import { EvidenceStatus } from '@/models/types';
import Button from '../atoms/Button.vue';
import Card from '../atoms/Card.vue';
import Modal from '../atoms/Modal.vue';
import { ref, watch, nextTick, computed } from 'vue';
import { useEvidences } from '@/hooks/useEvidences';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';
import { useToast } from 'vue-toast-notification';
const $toast = useToast();
const props = defineProps({
    uploadedEvidence: {
        type: Object as () => Evidence | undefined,
        required: false
    },
    evidence: {
        type: Object as () => Evidence,
        required: true
    },
    isEvidenceModalOpen: {
        type: Boolean,
        required: true
    },
    onModalClose: {
        type: Function,
        required: true
    },
    onEvidenceDelete: {
        type: Function,
        required: true
    },
    onEvidenceUpload: {
        type: Function,
        required: true
    },
    isAuditMode: {
        type: Boolean,
        default: false
    },
    isAppealMode: {
        type: Boolean,
        default: false
    },
    dimensionName: {
        type: String,
        default: ''
    }
});
const getDefaultTitle = (evidence: { approvalRequirements: { title: string }[] | string; id?: number }) => {
    if (!Array.isArray(evidence.approvalRequirements) || evidence.approvalRequirements.length === 0) {
        return 'Sin título';
    }
    const id = evidence.id ?? 0;
    const index = id % evidence.approvalRequirements.length;
    return evidence.approvalRequirements[index]?.title || 'Sin título';
};
const getDefaultRequirements = (evidence: { approvalRequirements: { requirement: string }[] | string; id?: number }) => {
    if (!Array.isArray(evidence.approvalRequirements) || evidence.approvalRequirements.length === 0) {
        return 'Sin título';
    }
    const id = evidence.id ?? 0;
    const index = id % evidence.approvalRequirements.length;
    return evidence.approvalRequirements[index]?.requirement || 'Sin necesidad de requisitos de aprovación';
};
const getEvidenceTitle = (evidence: Evidence | undefined): string => {
    if (!evidence) return "titulo";
    return evidence.title || getDefaultTitle(evidence);
};

const { handlePreviewEvidence, isLoading } = useEvidences();
const fileInput = ref<HTMLInputElement>();
const uploadedEvidence = ref<Evidence | undefined>(props.uploadedEvidence);
const hasChanges = ref(false);
const isUpdating = ref(false);
const errorMessage = ref('');

const onFileChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const file = target.files?.[0];
    if (file) {
        uploadedEvidence.value = {
            ...props.evidence,
            file: file,
            src: URL.createObjectURL(file),
            title: props.evidence.title || getDefaultTitle(props.evidence),
            ...(props.isAppealMode && props.evidence.id ? { 
                id: props.evidence.id,
                evidenceStatus: EvidenceStatus.APPEAL
            } : {})
        };
        hasChanges.value = true;
        errorMessage.value = '';
    }
};

const validateEvidenceData = (data: any): { isValid: boolean, missingFields: string[] } => {
    const requiredFields = ['id', 'title', 'type', 'approvalRequirements', 'file'];
    const missingFields = requiredFields.filter(field => {
        if (field === 'file') {
            return !data.file;
        }
        return data[field] === undefined || data[field] === null || data[field] === '';
    });
    
    return {
        isValid: missingFields.length === 0,
        missingFields
    };
};

const handleUploadEvidence = async () => {
    if (!uploadedEvidence.value) {
        errorMessage.value = "No hay evidencia para subir";
        $toast.error(errorMessage.value)
        return;
    }
    
    try {
        let evidenceToUpload: Evidence;
        
        if (props.isAppealMode && props.evidence.id) {
            evidenceToUpload = {
                ...props.evidence,
                ...uploadedEvidence.value,
                id: props.evidence.id,
                title: uploadedEvidence.value.title || getDefaultTitle(props.evidence),
                evidenceStatus: EvidenceStatus.APPEAL,
                file: uploadedEvidence.value.file
            };
            const validation = validateEvidenceData(evidenceToUpload);
            if (!validation.isValid) {
                const missingFieldsStr = validation.missingFields.join(', ');
                errorMessage.value = `Faltan campos requeridos para la apelación: ${missingFieldsStr}`;
                throw new Error(errorMessage.value);
            }
        } else {
            evidenceToUpload = {
                ...uploadedEvidence.value,
                title: uploadedEvidence.value.title || getDefaultTitle(props.evidence),
                type: uploadedEvidence.value.type || props.evidence.type,
                approvalRequirements: uploadedEvidence.value.approvalRequirements || props.evidence.approvalRequirements
            };
        }
        
        await props.onEvidenceUpload(evidenceToUpload);
        await nextTick();
        
        if (!props.isAuditMode && !props.isAppealMode) {
            window.location.reload();
        } else {
            isUpdating.value = false;
            $toast.success(props.isAppealMode ? 'Evidencia apelada correctamente' : 'Evidencia actualizada correctamente')
            window.location.reload();
            props.onModalClose();
        }
    } catch (error) {
        console.error("Error al subir la evidencia:", error);
        errorMessage.value = `Error al ${props.isAppealMode ? 'apelar' : 'actualizar'} la evidencia: ${error instanceof Error ? error.message : 'Error desconocido'}`;
        $toast.error(errorMessage.value);
    }
};

const { fetchData: handleUpload, isLoading: isUploadLoading } = useAsyncFetch(handleUploadEvidence);

const handleEvidenceDownload = () => {
    if (uploadedEvidence.value?.src) {
        const a = document.createElement('a');
        a.href = uploadedEvidence.value.src;
        a.download = getEvidenceTitle(uploadedEvidence.value);
        a.click();
    }
};

const handleEvidenceDelete = async (evidence: Evidence) => {
    if (!evidence) return;
    
    try {
        await props.onEvidenceDelete(evidence);
        uploadedEvidence.value = undefined;
        await nextTick();
        
        if (!props.isAuditMode && !props.isAppealMode) {
            window.location.reload();
        } else {
            alert('Evidencia eliminada correctamente');
            props.onModalClose();
        }
    } catch (error) {
        console.error("Error al eliminar la evidencia:", error);
        errorMessage.value = `Error al eliminar la evidencia: ${error instanceof Error ? error.message : 'Error desconocido'}`;
        alert(errorMessage.value);
    }
};

const startUpdateEvidence = () => {
    isUpdating.value = true;
    errorMessage.value = '';
};

const cancelUpdateEvidence = () => {
    isUpdating.value = false;
    errorMessage.value = '';
    if (props.evidence.id) {
        loadEvidencePreview(props.evidence);
    }
};

const loadEvidencePreview = async (evidence: Evidence) => {
    if (evidence.id) {
        try {
            const previewSrc = await handlePreviewEvidence(evidence.id);
            uploadedEvidence.value = {
                ...evidence,
                title: evidence.title || getDefaultTitle(props.evidence), 
                src: previewSrc
            };
            hasChanges.value = false;
        } catch (error) {
            console.error("Error loading evidence preview:", error);
            uploadedEvidence.value = { 
                ...evidence,
                title: evidence.title || getDefaultTitle(props.evidence)
            };
        }
    } else {
        uploadedEvidence.value = undefined;
    }
};

watch([uploadedEvidence, () => props.evidence], ([newUploadedEvidence, newEvidence]) => {
    if (newUploadedEvidence && newEvidence) {
        const isSameFile = newUploadedEvidence.file === newEvidence.file;
        hasChanges.value = !isSameFile;
    } else {
        hasChanges.value = false;
    }
}, { immediate: true });

watch(() => props.evidence, async (newEvidence) => {
    await loadEvidencePreview(newEvidence);
}, { immediate: true });
</script>

<template>
    <Modal :isOpen="isEvidenceModalOpen" :onClose="onModalClose">
        <div class="flex w-full max-w-[90vw] sm:max-w-[80vw] md:max-w-[75vw] lg:max-w-[70vw] xl:max-w-[65vw] 2xl:max-w-[60vw] h-auto max-h-[85vh] 2xl:max-h-[75vh] gap-4 sm:gap-6 lg:gap-8 flex-col lg:flex-row">
            <div class="flex h-full w-full lg:w-[60%] flex-col items-center justify-center" v-if="isLoading">
                <i v-if="!uploadedEvidence" class="animate-pulse fa-solid fa-file-arrow-up text-gray-500 text-6xl sm:text-8xl lg:text-9xl"></i>
            </div>
            <div v-else :class="uploadedEvidence ? 'relative p-0 px-4 sm:px-6 lg:px-10' : 'p-4 sm:p-6 lg:p-8'"
                class="flex w-full lg:w-[60%] flex-col justify-between items-center">
                <div v-if="!uploadedEvidence"></div>
                <i v-if="!uploadedEvidence" class="fa-solid fa-file-arrow-up text-primary-800 text-9xl"></i>
                <div v-else class="flex flex-col h-full w-full p-0">
                    <embed v-if="uploadedEvidence.src" class="w-full h-full object-cover rounded-md" :src="uploadedEvidence.src" />
                    <div v-else class="w-full h-full flex items-center justify-center bg-gray-100 rounded-md">
                        <span class="text-gray-500">No preview available</span>
                    </div>
                    <span class="flex flex-col gap-4 absolute text-primary-900 top-0 right-0">
                        <i v-if="uploadedEvidence && (isUpdating || !isAuditMode || isAppealMode)" 
                           @click="() => uploadedEvidence && handleEvidenceDelete(uploadedEvidence)"
                           class="fa-solid fa-trash-can text-xl cursor-pointer"></i>
                        <i v-if="uploadedEvidence?.src" @click="handleEvidenceDownload" 
                           class="fa-solid fa-download text-xl cursor-pointer"></i>
                        <i v-if="isAuditMode && !isUpdating && !isAppealMode && uploadedEvidence" 
                           @click="startUpdateEvidence"
                           class="fa-solid fa-pen-to-square text-xl cursor-pointer"></i>
                    </span>
                </div>
                <div class="mt-4 flex gap-4">
                    <div v-if="!isAuditMode">
                        <Button v-if="!uploadedEvidence || isUpdating || isAppealMode" @click="fileInput?.click()">
                        {{ isAppealMode ? 'Subir Apelación' : (isUpdating ? 'Cambiar Evidencia' : 'Subir Evidencia') }}
                        <input ref="fileInput" type="file" class="hidden" :accept="`.${evidence.type}`"
                            @change="onFileChange">
                    </Button>
                    <Button v-if="isUpdating && !isAppealMode" @click="cancelUpdateEvidence" variant="secondary">
                        Cancelar
                    </Button>
                    </div>
                </div>
                <div v-if="errorMessage" class="mt-4 p-2 bg-red-100 text-red-800 rounded-md text-sm w-full">
                    {{ errorMessage }}
                </div>
            </div>
            <Card :style="{ backgroundColor: '#40189d' }" class="flex-1 w-full lg:w-auto flex flex-col justify-between gap-4 sm:gap-6 lg:gap-8 text-white min-h-[300px] lg:min-h-[400px]">
                <slot name="modal-content" :evidence="evidence">
                    <div class="flex flex-col gap-8">
                        <div class="flex flex-col gap-5 items-center">
                            <h3 class="flex flex-col gap-2 text-lg sm:text-xl lg:text-2xl font-bold">
                                {{ isAppealMode ? 'Apelación de Evidencia' : 'Condiciones de Aprobación' }}
                                <p class="text-center text-xs sm:text-sm font-semibold italic text-white/80">
                                    {{ getEvidenceTitle(evidence) }}
                                </p>
                            </h3>
                            <p class="text-sm sm:text-base">
                                {{ isAppealMode
                                    ? 'Para apelar esta evidencia, sube un nuevo archivo que cumpla con los requisitos:'
                                    : 'Para que la evidencia presentada sea aprobada, debe incluir los siguientes elementos:' }}
                            </p>
                        </div>
                        <ul class="flex flex-col gap-3">
                            <li class="text-sm font-semibold">
                                <p>* Formato: <span class="uppercase">{{ evidence.type }}</span></p>
                            </li>
                            <div v-if="isAppealMode!=true">
                                <p class="2xl:text-base xl:text-sm">{{ evidence.approvalRequirements }}</p>
                            </div>
                            <div v-else>
                                <p class="2xl:text-base xl:text-sm">{{ getDefaultRequirements(evidence) }}</p>
                            </div>
                        </ul>
                    </div>
                </slot>

                <slot name="evidence-actions">
                    <Button 
                        :loading="isUploadLoading" 
                        :disabled="!uploadedEvidence || (!hasChanges && !isUpdating && !isAppealMode)"
                        @click="handleUpload" 
                        class="w-40" 
                        variant="invert">
                        {{ isAppealMode ? 'Actualizar' : (isAuditMode ? 'Actualizar' : 'Guardar') }}
                    </Button>
                </slot>
            </Card>
        </div>
    </Modal>
</template>