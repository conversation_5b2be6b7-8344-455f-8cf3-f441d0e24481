<script setup lang="ts">
import { useClickOutside } from '@/hooks/useClickOutside';
import { ref, type Ref } from 'vue';
const props = defineProps({
    refs: {
        type: Array as () => Ref<HTMLElement | null>[],
        required: false,
    },
    isOpen: {
        type: Boolean,
        required: true,
    },
    onClose: {
        type: Function,
        required: true,
    },
    title: String,
})

const modalRef = ref<HTMLElement | null>(null);

useClickOutside(props.refs ? [...props.refs, modalRef] : [modalRef], () => {
    props.onClose();
});

</script>

<template>
    <transition name="modal">
        <div v-if="isOpen" class="fixed inset-0 z-50 bg-black/25 flex justify-center items-center p-4">
            <div ref="modalRef"
                class="bg-white max-h-[90vh] w-full max-w-[98vw] xs:max-w-[95vw] sm:max-w-[85vw] md:max-w-[80vw] lg:max-w-[70vw] xl:max-w-[60vw] hd-720:max-w-[65vw] hd-ready:max-w-[60vw] overflow-y-auto rounded-2xl sm:rounded-3xl flex flex-col gap-3 sm:gap-4 lg:gap-6 p-3 sm:p-4 lg:p-8 relative shadow-2xl mx-2 sm:mx-4">
                <slot></slot>
            </div>
        </div>
    </transition>
</template>

<style scoped>
.modal-enter-active,
.modal-leave-active {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-enter-active > div,
.modal-leave-active > div {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-enter-from {
    opacity: 0;
}

.modal-enter-from > div {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
}

.modal-enter-to {
    opacity: 1;
}

.modal-enter-to > div {
    opacity: 1;
    transform: scale(1) translateY(0);
}

.modal-leave-from {
    opacity: 1;
}

.modal-leave-from > div {
    opacity: 1;
    transform: scale(1) translateY(0);
}

.modal-leave-to {
    opacity: 0;
}

.modal-leave-to > div {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
}

/* Mejoras para pantallas pequeñas */
@media (max-width: 640px) {
    .modal-enter-from > div,
    .modal-leave-to > div {
        transform: scale(0.9) translateY(-10px);
    }
}

/* Optimizaciones para resoluciones específicas */
@media (max-width: 1366px) and (max-height: 768px) {
    .modal-enter-from > div,
    .modal-leave-to > div {
        transform: scale(0.98) translateY(-10px);
    }
}
</style>